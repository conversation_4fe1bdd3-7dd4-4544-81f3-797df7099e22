---
export interface Props {
  category: string;
  variant?: 'default' | 'light';
  size?: 'sm' | 'md';
}

const { category, variant = 'default', size = 'md' } = Astro.props;

// Category color mapping
const categoryColors: Record<string, { bg: string; text: string; lightBg: string; lightText: string }> = {
  'web-development': {
    bg: 'bg-blue-100 dark:bg-blue-900/30',
    text: 'text-blue-800 dark:text-blue-300',
    lightBg: 'bg-blue-500/20',
    lightText: 'text-blue-100'
  },
  'javascript': {
    bg: 'bg-yellow-100 dark:bg-yellow-900/30',
    text: 'text-yellow-800 dark:text-yellow-300',
    lightBg: 'bg-yellow-500/20',
    lightText: 'text-yellow-100'
  },
  'technology': {
    bg: 'bg-purple-100 dark:bg-purple-900/30',
    text: 'text-purple-800 dark:text-purple-300',
    lightBg: 'bg-purple-500/20',
    lightText: 'text-purple-100'
  },
  'tutorials': {
    bg: 'bg-green-100 dark:bg-green-900/30',
    text: 'text-green-800 dark:text-green-300',
    lightBg: 'bg-green-500/20',
    lightText: 'text-green-100'
  },
  'tips': {
    bg: 'bg-orange-100 dark:bg-orange-900/30',
    text: 'text-orange-800 dark:text-orange-300',
    lightBg: 'bg-orange-500/20',
    lightText: 'text-orange-100'
  },
  'react': {
    bg: 'bg-cyan-100 dark:bg-cyan-900/30',
    text: 'text-cyan-800 dark:text-cyan-300',
    lightBg: 'bg-cyan-500/20',
    lightText: 'text-cyan-100'
  },
  'astro': {
    bg: 'bg-indigo-100 dark:bg-indigo-900/30',
    text: 'text-indigo-800 dark:text-indigo-300',
    lightBg: 'bg-indigo-500/20',
    lightText: 'text-indigo-100'
  }
};

// Normalize category for lookup
const normalizedCategory = category.toLowerCase().replace(/\s+/g, '-');
const colors = categoryColors[normalizedCategory] || categoryColors['technology'];

// Size classes
const sizeClasses = {
  sm: 'px-2 py-1 text-xs',
  md: 'px-3 py-1 text-sm'
};

// Variant classes
const variantClasses = variant === 'light' 
  ? `${colors.lightBg} ${colors.lightText} border border-white/30 backdrop-blur-sm`
  : `${colors.bg} ${colors.text}`;

const categorySlug = normalizedCategory;
---

<a 
  href={`/categories/${categorySlug}`}
  class={`inline-flex items-center font-medium rounded-full transition-all duration-200 hover:scale-105 hover:shadow-md ${sizeClasses[size]} ${variantClasses}`}
  title={`View all posts in ${category}`}
>
  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
    <path fill-rule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
  </svg>
  {category}
</a>
