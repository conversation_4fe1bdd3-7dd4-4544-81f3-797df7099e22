---
// No server-side props needed for search bar
---

<div class="relative">
  <div class="relative">
    <input
      type="text"
      id="search-input"
      placeholder="Search posts..."
      class="w-full sm:w-64 px-4 py-2 pl-10 pr-4 text-sm bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200"
      autocomplete="off"
    />
    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
      <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
      </svg>
    </div>
    <button
      id="search-clear"
      class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors hidden"
      aria-label="Clear search"
    >
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    </button>
  </div>
  
  <!-- Search Results Dropdown -->
  <div
    id="search-results"
    class="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-h-96 overflow-y-auto z-50 hidden"
  >
    <div id="search-results-content" class="p-2">
      <!-- Results will be populated here -->
    </div>
    <div id="search-no-results" class="p-4 text-center text-gray-500 dark:text-gray-400 text-sm hidden">
      No posts found matching your search.
    </div>
  </div>
</div>

<script>
  // Search functionality
  const searchInput = document.getElementById('search-input') as HTMLInputElement;
  const searchClear = document.getElementById('search-clear') as HTMLButtonElement;
  const searchResults = document.getElementById('search-results') as HTMLDivElement;
  const searchResultsContent = document.getElementById('search-results-content') as HTMLDivElement;
  const searchNoResults = document.getElementById('search-no-results') as HTMLDivElement;

  let searchData: any[] = [];
  let searchTimeout: NodeJS.Timeout;

  // Load search data (in a real implementation, this would fetch from an API or static JSON)
  async function loadSearchData() {
    try {
      // This is a placeholder - in a real implementation, you'd fetch this data
      // from a JSON file generated at build time or from an API
      searchData = [
        {
          title: "Getting Started with Astro",
          description: "Learn how to build fast, modern websites with Astro",
          slug: "getting-started-with-astro",
          category: "tutorials",
          tags: ["astro", "web-development"]
        },
        {
          title: "JavaScript Best Practices",
          description: "Essential JavaScript patterns and practices for modern development",
          slug: "javascript-best-practices",
          category: "javascript",
          tags: ["javascript", "tips"]
        },
        {
          title: "Building Responsive Layouts",
          description: "Master CSS Grid and Flexbox for responsive web design",
          slug: "building-responsive-layouts",
          category: "web-development",
          tags: ["css", "responsive", "layout"]
        }
      ];
    } catch (error) {
      console.error('Failed to load search data:', error);
    }
  }

  // Search function
  function performSearch(query: string) {
    if (!query.trim()) {
      hideSearchResults();
      return;
    }

    const results = searchData.filter(post => 
      post.title.toLowerCase().includes(query.toLowerCase()) ||
      post.description.toLowerCase().includes(query.toLowerCase()) ||
      post.tags.some((tag: string) => tag.toLowerCase().includes(query.toLowerCase())) ||
      post.category.toLowerCase().includes(query.toLowerCase())
    );

    displaySearchResults(results, query);
  }

  // Display search results
  function displaySearchResults(results: any[], query: string) {
    if (results.length === 0) {
      searchResultsContent.innerHTML = '';
      searchNoResults.classList.remove('hidden');
    } else {
      searchNoResults.classList.add('hidden');
      searchResultsContent.innerHTML = results.map(post => `
        <a href="/blog/${post.slug}" class="block p-3 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
          <div class="font-medium text-gray-900 dark:text-white text-sm mb-1">${highlightText(post.title, query)}</div>
          <div class="text-gray-600 dark:text-gray-300 text-xs mb-2 line-clamp-2">${highlightText(post.description, query)}</div>
          <div class="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
            <span class="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded">${post.category}</span>
            ${post.tags.slice(0, 2).map((tag: string) => `<span>#${tag}</span>`).join(' ')}
          </div>
        </a>
      `).join('');
    }
    
    searchResults.classList.remove('hidden');
  }

  // Highlight search terms
  function highlightText(text: string, query: string): string {
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">$1</mark>');
  }

  // Hide search results
  function hideSearchResults() {
    searchResults.classList.add('hidden');
  }

  // Show/hide clear button
  function toggleClearButton() {
    if (searchInput.value.trim()) {
      searchClear.classList.remove('hidden');
    } else {
      searchClear.classList.add('hidden');
    }
  }

  // Event listeners
  searchInput?.addEventListener('input', (e) => {
    const query = (e.target as HTMLInputElement).value;
    toggleClearButton();
    
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
      performSearch(query);
    }, 300);
  });

  searchInput?.addEventListener('focus', () => {
    if (searchInput.value.trim()) {
      performSearch(searchInput.value);
    }
  });

  searchClear?.addEventListener('click', () => {
    searchInput.value = '';
    toggleClearButton();
    hideSearchResults();
    searchInput.focus();
  });

  // Hide results when clicking outside
  document.addEventListener('click', (e) => {
    if (!searchInput?.contains(e.target as Node) && !searchResults?.contains(e.target as Node)) {
      hideSearchResults();
    }
  });

  // Load search data on page load
  loadSearchData();
</script>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
