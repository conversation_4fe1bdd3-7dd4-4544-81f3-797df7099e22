---
import CategoryTag from './CategoryTag.astro';
import { formatDate } from '../utils/formatDate';

export interface Props {
  post: {
    slug: string;
    data: {
      title: string;
      description: string;
      pubDate: Date;
      heroImage?: string;
      category: string;
      tags: string[];
      author: string;
    };
  };
}

const { post } = Astro.props;
const { slug, data } = post;
const { title, description, pubDate, heroImage, category, tags, author } = data;
---

<article class="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl overflow-hidden group">
  <a href={`/blog/${slug}`} class="block">
    <!-- Hero Image -->
    <div class="relative aspect-[16/9] md:aspect-[21/9] overflow-hidden">
      {heroImage ? (
        <img 
          src={heroImage} 
          alt={title}
          class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
        />
      ) : (
        <div class="w-full h-full bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 flex items-center justify-center">
          <svg class="w-24 h-24 text-white/80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
          </svg>
        </div>
      )}
      
      <!-- Featured Badge -->
      <div class="absolute top-4 left-4">
        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-500 text-white shadow-lg">
          <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
          Featured
        </span>
      </div>
      
      <!-- Gradient Overlay -->
      <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
    </div>
    
    <!-- Content Overlay -->
    <div class="absolute bottom-0 left-0 right-0 p-6 md:p-8 text-white">
      <!-- Category -->
      <div class="mb-3">
        <CategoryTag category={category} variant="light" />
      </div>
      
      <!-- Title -->
      <h2 class="text-2xl md:text-4xl font-bold mb-3 group-hover:text-yellow-300 transition-colors line-clamp-2">
        {title}
      </h2>
      
      <!-- Description -->
      <p class="text-gray-200 mb-4 text-lg line-clamp-2 max-w-3xl">
        {description}
      </p>
      
      <!-- Meta Information -->
      <div class="flex items-center justify-between text-sm text-gray-300">
        <div class="flex items-center space-x-4">
          <span>By {author}</span>
          <span>•</span>
          <time datetime={pubDate.toISOString()}>
            {formatDate(pubDate)}
          </time>
        </div>
        
        <!-- Read More Arrow -->
        <div class="flex items-center space-x-2 group-hover:translate-x-1 transition-transform">
          <span class="hidden sm:inline">Read More</span>
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
          </svg>
        </div>
      </div>
    </div>
  </a>
  
  <!-- Tags (positioned outside the overlay for better visibility) -->
  {tags.length > 0 && (
    <div class="absolute top-4 right-4 flex flex-wrap gap-2 max-w-xs">
      {tags.slice(0, 3).map((tag) => (
        <span class="px-2 py-1 text-xs font-medium bg-white/20 backdrop-blur-sm text-white rounded border border-white/30">
          #{tag}
        </span>
      ))}
    </div>
  )}
</article>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
