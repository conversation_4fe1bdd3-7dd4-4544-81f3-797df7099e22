---
import ThemeToggle from './ThemeToggle.astro';
import SearchBar from './SearchBar.astro';

const currentPath = Astro.url.pathname;
---

<header class="header">
  <nav class="nav-container">
    <div class="nav-content">
      <!-- Logo/Brand -->
      <div class="brand-section">
        <a href="/" class="brand-link">
          <svg class="brand-icon" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
          </svg>
          <span>My Blog</span>
        </a>
      </div>

      <!-- Desktop Navigation -->
      <div class="desktop-nav">
        <a
          href="/"
          class={`nav-link ${currentPath === '/' ? 'active' : ''}`}
        >
          Home
        </a>
        <a
          href="/blog"
          class={`nav-link ${currentPath.startsWith('/blog') ? 'active' : ''}`}
        >
          Blog
        </a>
        <a
          href="/about"
          class={`nav-link ${currentPath === '/about' ? 'active' : ''}`}
        >
          About
        </a>
      </div>

      <!-- Search and Theme Toggle -->
      <div class="nav-actions">
        <div class="search-container">
          <SearchBar />
        </div>
        <ThemeToggle />

        <!-- Mobile menu button -->
        <button
          id="mobile-menu-button"
          class="mobile-menu-btn"
          aria-label="Toggle mobile menu"
        >
          <svg class="menu-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Navigation -->
    <div id="mobile-menu" class="mobile-nav">
      <div class="mobile-nav-content">
        <a
          href="/"
          class={`mobile-nav-link ${currentPath === '/' ? 'active' : ''}`}
        >
          Home
        </a>
        <a
          href="/blog"
          class={`mobile-nav-link ${currentPath.startsWith('/blog') ? 'active' : ''}`}
        >
          Blog
        </a>
        <a
          href="/about"
          class={`mobile-nav-link ${currentPath === '/about' ? 'active' : ''}`}
        >
          About
        </a>
        <div class="mobile-search">
          <SearchBar />
        </div>
      </div>
    </div>
  </nav>
</header>

<style>
  .header {
    position: sticky;
    top: 0;
    z-index: 50;
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(8px);
    border-bottom: 1px solid var(--color-border);
  }

  [data-theme="dark"] .header {
    background-color: rgba(17, 24, 39, 0.8);
  }

  .nav-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
  }

  @media (min-width: 640px) {
    .nav-container {
      padding: 0 var(--spacing-6);
    }
  }

  @media (min-width: 1024px) {
    .nav-container {
      padding: 0 var(--spacing-8);
    }
  }

  .nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 4rem;
  }

  .brand-section {
    display: flex;
    align-items: center;
  }

  .brand-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--color-text-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
  }

  .brand-link:hover {
    color: var(--color-primary-600);
  }

  [data-theme="dark"] .brand-link:hover {
    color: var(--color-primary-400);
  }

  .brand-icon {
    width: 2rem;
    height: 2rem;
    color: var(--color-primary-600);
  }

  [data-theme="dark"] .brand-icon {
    color: var(--color-primary-400);
  }

  .desktop-nav {
    display: none;
    align-items: center;
    gap: var(--spacing-8);
  }

  @media (min-width: 768px) {
    .desktop-nav {
      display: flex;
    }
  }

  .nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
  }

  .search-container {
    display: none;
  }

  @media (min-width: 640px) {
    .search-container {
      display: block;
    }
  }

  .mobile-menu-btn {
    display: flex;
    padding: var(--spacing-2);
    border-radius: var(--border-radius-md);
    color: var(--color-text-secondary);
    background: none;
    border: none;
    cursor: pointer;
    transition: all var(--transition-fast);
  }

  .mobile-menu-btn:hover {
    color: var(--color-primary-600);
    background-color: var(--color-bg-tertiary);
  }

  [data-theme="dark"] .mobile-menu-btn:hover {
    color: var(--color-primary-400);
  }

  @media (min-width: 768px) {
    .mobile-menu-btn {
      display: none;
    }
  }

  .menu-icon {
    width: 1.5rem;
    height: 1.5rem;
  }

  .mobile-nav {
    display: none;
    padding-top: var(--spacing-4);
    padding-bottom: var(--spacing-2);
  }

  .mobile-nav.show {
    display: block;
  }

  @media (min-width: 768px) {
    .mobile-nav {
      display: none !important;
    }
  }

  .mobile-nav-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .mobile-nav-link {
    display: block;
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--border-radius-md);
    color: var(--color-text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: all var(--transition-fast);
  }

  .mobile-nav-link:hover {
    color: var(--color-primary-600);
    background-color: var(--color-bg-tertiary);
  }

  .mobile-nav-link.active {
    color: var(--color-primary-600);
    background-color: var(--color-primary-50);
  }

  [data-theme="dark"] .mobile-nav-link:hover {
    color: var(--color-primary-400);
  }

  [data-theme="dark"] .mobile-nav-link.active {
    color: var(--color-primary-400);
    background-color: rgba(59, 130, 246, 0.1);
  }

  .mobile-search {
    padding-top: var(--spacing-2);
  }

  @media (min-width: 640px) {
    .mobile-search {
      display: none;
    }
  }
</style>

<script>
  // Mobile menu toggle
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');

  mobileMenuButton?.addEventListener('click', () => {
    mobileMenu?.classList.toggle('show');
  });
</script>

<script>
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  
  mobileMenuButton?.addEventListener('click', () => {
    mobileMenu?.classList.toggle('hidden');
  });
</script>