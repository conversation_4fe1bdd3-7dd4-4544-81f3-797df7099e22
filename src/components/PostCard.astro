---
export interface Props {
  title: string;
  description: string;
  pubDate: Date;
  heroImage?: string;
  category: string;
  slug: string;
  readingTime: string;
  featured?: boolean;
}

const { title, description, pubDate, heroImage, category, slug, readingTime, featured } = Astro.props;
const formattedDate = pubDate.toLocaleDateString('en-US', { 
  year: 'numeric', 
  month: 'long', 
  day: 'numeric' 
});
---

<article class={`group relative bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden border border-gray-200 dark:border-gray-700 ${featured ? 'ring-2 ring-primary-500' : ''}`}>
  {heroImage && (
    <div class="aspect-[16/9] overflow-hidden">
      <img 
        src={heroImage} 
        alt={title}
        class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        loading="lazy"
      />
    </div>
  )}
  
  <div class="p-6">
    <div class="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-3">
      <span class="bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 px-2 py-1 rounded-md font-medium">
        {category}
      </span>
      <time datetime={pubDate.toISOString()}>
        {formattedDate}
      </time>
      <span>·</span>
      <span>{readingTime}</span>
    </div>
    
    <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
      <a href={`/blog/${slug}`} class="stretched-link">
        {title}
      </a>
    </h2>
    
    <p class="text-gray-600 dark:text-gray-300 line-clamp-3">
      {description}
    </p>
    
    {featured && (
      <div class="absolute top-4 right-4">
        <span class="bg-primary-500 text-white px-2 py-1 text-xs font-medium rounded-full">
          Featured
        </span>
      </div>
    )}
  </div>
</article>

<style>
  .stretched-link::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    content: "";
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>