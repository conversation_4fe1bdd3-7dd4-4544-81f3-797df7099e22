---
import CategoryTag from './CategoryTag.astro';
import { formatDate } from '../utils/formatDate';

export interface Props {
  post: {
    slug: string;
    data: {
      title: string;
      description: string;
      pubDate: Date;
      heroImage?: string;
      category: string;
      tags: string[];
      author: string;
      featured?: boolean;
    };
  };
}

const { post } = Astro.props;
const { slug, data } = post;
const { title, description, pubDate, heroImage, category, tags, author, featured } = data;
---

<article class="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden group">
  <a href={`/blog/${slug}`} class="block">
    <!-- Hero Image -->
    {heroImage ? (
      <div class="aspect-video overflow-hidden">
        <img
          src={heroImage}
          alt={title}
          class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          loading="lazy"
        />
      </div>
    ) : (
      <div class="aspect-video bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
        <svg class="w-16 h-16 text-white/80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
        </svg>
      </div>
    )}

    <!-- Content -->
    <div class="p-6">
      <!-- Category -->
      <div class="mb-3">
        <CategoryTag category={category} />
      </div>

      <!-- Title -->
      <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors line-clamp-2">
        {title}
      </h3>

      <!-- Description -->
      <p class="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
        {description}
      </p>

      <!-- Meta Information -->
      <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
        <div class="flex items-center space-x-2">
          <span>By {author}</span>
        </div>
        <time datetime={pubDate.toISOString()}>
          {formatDate(pubDate)}
        </time>
      </div>

      <!-- Tags -->
      {tags.length > 0 && (
        <div class="mt-4 flex flex-wrap gap-2">
          {tags.slice(0, 3).map((tag) => (
            <span class="px-2 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded">
              #{tag}
            </span>
          ))}
          {tags.length > 3 && (
            <span class="px-2 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded">
              +{tags.length - 3} more
            </span>
          )}
        </div>
      )}
    </div>
  </a>
</article>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>