---
import { getCollection } from 'astro:content';
import PostLayout from '../../layouts/PostLayout.astro';

export async function getStaticPaths() {
  const blogEntries = await getCollection('blog', ({ data }) => {
    return !data.draft;
  });
  
  return blogEntries.map(entry => ({
    params: { slug: entry.slug },
    props: { entry },
  }));
}

const { entry } = Astro.props;
const { Content } = await entry.render();
---

<PostLayout frontmatter={entry.data}>
  <Content />
</PostLayout>
