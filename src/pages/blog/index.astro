---
import { getCollection } from 'astro:content';
import BaseLayout from '../../layouts/BaseLayout.astro';
import PostCard from '../../components/PostCard.astro';

// Get all blog posts
const allPosts = await getCollection('blog', ({ data }) => {
  return !data.draft;
});

// Sort posts by publication date (newest first)
const sortedPosts = allPosts.sort((a, b) => 
  new Date(b.data.pubDate).getTime() - new Date(a.data.pubDate).getTime()
);

// Get unique categories
const categories = [...new Set(allPosts.map(post => post.data.category))];

// Pagination (you can implement this later)
const postsPerPage = 12;
const totalPages = Math.ceil(sortedPosts.length / postsPerPage);
const currentPage = 1;
const paginatedPosts = sortedPosts.slice(0, postsPerPage);
---

<BaseLayout 
  title="Blog" 
  description="Explore all articles covering web development, programming tutorials, and technology insights."
>
  <!-- Header Section -->
  <section class="bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
          Blog
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Explore articles covering web development, programming tutorials, and technology insights.
        </p>
      </div>
    </div>
  </section>

  <!-- Filter Section -->
  <section class="py-8 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex flex-wrap items-center justify-between gap-4">
        <div class="flex items-center gap-4">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Filter by category:</span>
          <div class="flex flex-wrap gap-2">
            <a 
              href="/blog" 
              class="px-3 py-1 text-sm font-medium rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors"
            >
              All
            </a>
            {categories.map((category) => (
              <a 
                href={`/categories/${category.toLowerCase().replace(/\s+/g, '-')}`}
                class="px-3 py-1 text-sm font-medium rounded-full bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              >
                {category}
              </a>
            ))}
          </div>
        </div>
        
        <div class="text-sm text-gray-500 dark:text-gray-400">
          {sortedPosts.length} {sortedPosts.length === 1 ? 'article' : 'articles'}
        </div>
      </div>
    </div>
  </section>

  <!-- Posts Grid -->
  <section class="py-16 bg-white dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {paginatedPosts.length > 0 ? (
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {paginatedPosts.map((post) => (
            <PostCard post={post} />
          ))}
        </div>
      ) : (
        <div class="text-center py-16">
          <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
          </svg>
          <h3 class="text-xl font-medium text-gray-900 dark:text-white mb-2">No posts found</h3>
          <p class="text-gray-500 dark:text-gray-400">Check back soon for new content!</p>
        </div>
      )}
    </div>
  </section>

  <!-- Pagination (placeholder for future implementation) -->
  {totalPages > 1 && (
    <section class="py-8 bg-gray-50 dark:bg-gray-800">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-center items-center space-x-4">
          <button 
            disabled={currentPage === 1}
            class="px-4 py-2 text-sm font-medium text-gray-500 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
          >
            Previous
          </button>
          
          <span class="text-sm text-gray-700 dark:text-gray-300">
            Page {currentPage} of {totalPages}
          </span>
          
          <button 
            disabled={currentPage === totalPages}
            class="px-4 py-2 text-sm font-medium text-gray-500 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
          >
            Next
          </button>
        </div>
      </div>
    </section>
  )}
</BaseLayout>
