---
import { getCollection } from 'astro:content';
import BaseLayout from '../../layouts/BaseLayout.astro';
import PostCard from '../../components/PostCard.astro';

export async function getStaticPaths() {
  const allPosts = await getCollection('blog', ({ data }) => {
    return !data.draft;
  });
  
  // Get unique categories
  const categories = [...new Set(allPosts.map(post => post.data.category))];
  
  return categories.map(category => {
    const categorySlug = category.toLowerCase().replace(/\s+/g, '-');
    const filteredPosts = allPosts.filter(post => post.data.category === category);
    
    return {
      params: { category: categorySlug },
      props: { 
        category,
        posts: filteredPosts.sort((a, b) => 
          new Date(b.data.pubDate).getTime() - new Date(a.data.pubDate).getTime()
        )
      },
    };
  });
}

const { category, posts } = Astro.props;
const categoryTitle = category.charAt(0).toUpperCase() + category.slice(1);
---

<BaseLayout 
  title={`${categoryTitle} Articles`}
  description={`Explore all articles in the ${category} category.`}
>
  <!-- Header Section -->
  <section class="bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <nav class="mb-6">
          <ol class="flex items-center justify-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
            <li>
              <a href="/" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Home</a>
            </li>
            <li>
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
            </li>
            <li>
              <a href="/blog" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Blog</a>
            </li>
            <li>
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
            </li>
            <li class="text-gray-700 dark:text-gray-300 font-medium">{categoryTitle}</li>
          </ol>
        </nav>
        
        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
          {categoryTitle} Articles
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Discover {posts.length} {posts.length === 1 ? 'article' : 'articles'} in the {category} category.
        </p>
      </div>
    </div>
  </section>

  <!-- Posts Grid -->
  <section class="py-16 bg-white dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {posts.length > 0 ? (
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {posts.map((post) => (
            <PostCard post={post} />
          ))}
        </div>
      ) : (
        <div class="text-center py-16">
          <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
          </svg>
          <h3 class="text-xl font-medium text-gray-900 dark:text-white mb-2">No posts found</h3>
          <p class="text-gray-500 dark:text-gray-400">No articles in this category yet. Check back soon!</p>
        </div>
      )}
    </div>
  </section>

  <!-- Back to Blog -->
  <section class="py-8 bg-gray-50 dark:bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <a 
        href="/blog" 
        class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200"
      >
        <svg class="mr-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
        </svg>
        Back to All Posts
      </a>
    </div>
  </section>
</BaseLayout>
