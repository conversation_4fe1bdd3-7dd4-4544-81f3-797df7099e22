---
import { getCollection } from 'astro:content';
import BaseLayout from '../layouts/BaseLayout.astro';
import FeaturedPost from '../components/FeaturedPost.astro';
import PostCard from '../components/PostCard.astro';

// Get all blog posts
const allPosts = await getCollection('blog', ({ data }) => {
  return !data.draft;
});

// Sort posts by publication date (newest first)
const sortedPosts = allPosts.sort((a, b) =>
  new Date(b.data.pubDate).getTime() - new Date(a.data.pubDate).getTime()
);

// Get featured post (first post marked as featured, or the latest post)
const featuredPost = sortedPosts.find(post => post.data.featured) || sortedPosts[0];

// Get recent posts (excluding the featured post)
const recentPosts = sortedPosts
  .filter(post => post.slug !== featuredPost?.slug)
  .slice(0, 6);
---

<BaseLayout
  title="Welcome to My Blog"
  description="Discover the latest insights on web development, technology, and programming tutorials."
>
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 py-16 md:py-24">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <h1 class="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
          Welcome to <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">My Blog</span>
        </h1>
        <p class="text-xl md:text-2xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
          Discover the latest insights on web development, technology, and programming tutorials.
          Join me on a journey through the ever-evolving world of code.
        </p>
        <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
          <a
            href="/blog"
            class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200 shadow-lg hover:shadow-xl"
          >
            Explore Articles
            <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
            </svg>
          </a>
          <a
            href="/about"
            class="inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 shadow-lg hover:shadow-xl"
          >
            About Me
          </a>
        </div>
      </div>

      <!-- Featured Post -->
      {featuredPost && (
        <div class="mb-16">
          <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">Featured Article</h2>
          <FeaturedPost post={featuredPost} />
        </div>
      )}
    </div>
  </section>

  <!-- Recent Posts Section -->
  <section class="py-16 bg-white dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
          Latest Articles
        </h2>
        <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          Stay up to date with the latest posts covering web development, programming tips, and technology insights.
        </p>
      </div>

      {recentPosts.length > 0 ? (
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {recentPosts.map((post) => (
            <PostCard post={post} />
          ))}
        </div>
      ) : (
        <div class="text-center py-12">
          <p class="text-gray-500 dark:text-gray-400 text-lg">No posts available yet. Check back soon!</p>
        </div>
      )}

      <div class="text-center">
        <a
          href="/blog"
          class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200"
        >
          View All Posts
          <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>

  <!-- Newsletter Section -->
  <section class="py-16 bg-gray-50 dark:bg-gray-800">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
        Stay Updated
      </h2>
      <p class="text-lg text-gray-600 dark:text-gray-300 mb-8">
        Get notified when I publish new articles. No spam, unsubscribe at any time.
      </p>
      <form class="max-w-md mx-auto flex gap-4">
        <input
          type="email"
          placeholder="Enter your email"
          class="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
          required
        />
        <button
          type="submit"
          class="px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
        >
          Subscribe
        </button>
      </form>
    </div>
  </section>
</BaseLayout>
