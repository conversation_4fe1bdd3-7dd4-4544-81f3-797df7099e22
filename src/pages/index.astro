---
import { getCollection } from 'astro:content';
import BaseLayout from '../layouts/BaseLayout.astro';
import FeaturedPost from '../components/FeaturedPost.astro';
import PostCard from '../components/PostCard.astro';

// Get all blog posts
const allPosts = await getCollection('blog', ({ data }) => {
  return !data.draft;
});

// Sort posts by publication date (newest first)
const sortedPosts = allPosts.sort((a, b) =>
  new Date(b.data.pubDate).getTime() - new Date(a.data.pubDate).getTime()
);

// Get featured post (first post marked as featured, or the latest post)
const featuredPost = sortedPosts.find(post => post.data.featured) || sortedPosts[0];

// Get recent posts (excluding the featured post)
const recentPosts = sortedPosts
  .filter(post => post.slug !== featuredPost?.slug)
  .slice(0, 6);
---

<BaseLayout
  title="Welcome to My Blog"
  description="Discover the latest insights on web development, technology, and programming tutorials."
>
  <!-- Hero Section -->
  <section class="hero-section">
    <div class="container">
      <div class="hero-content">
        <h1 class="hero-title">
          Welcome to <span class="text-gradient">My Blog</span>
        </h1>
        <p class="hero-description">
          Discover the latest insights on web development, technology, and programming tutorials.
          Join me on a journey through the ever-evolving world of code.
        </p>
        <div class="hero-actions">
          <a href="/blog" class="btn btn-primary">
            Explore Articles
            <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
            </svg>
          </a>
          <a href="/about" class="btn btn-secondary">
            About Me
          </a>
        </div>
      </div>

      <!-- Featured Post -->
      {featuredPost && (
        <div class="featured-section">
          <h2 class="section-title">Featured Article</h2>
          <FeaturedPost post={featuredPost} />
        </div>
      )}
    </div>
  </section>

  <!-- Recent Posts Section -->
  <section class="recent-posts-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">
          Latest Articles
        </h2>
        <p class="section-description">
          Stay up to date with the latest posts covering web development, programming tips, and technology insights.
        </p>
      </div>

      {recentPosts.length > 0 ? (
        <div class="posts-grid"
          {recentPosts.map((post) => (
            <PostCard post={post} />
          ))}
        </div>
      ) : (
        <div class="empty-state">
          <p>No posts available yet. Check back soon!</p>
        </div>
      )}

      <div class="view-all-container">
        <a href="/blog" class="view-all-link">
          View All Posts
          <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>

  <!-- Newsletter Section -->
  <section class="newsletter-section">
    <div class="newsletter-container">
      <h2 class="newsletter-title">
        Stay Updated
      </h2>
      <p class="newsletter-description">
        Get notified when I publish new articles. No spam, unsubscribe at any time.
      </p>
      <form class="newsletter-form">
        <input
          type="email"
          placeholder="Enter your email"
          class="newsletter-input"
          required
        />
        <button type="submit" class="btn btn-primary">
          Subscribe
        </button>
      </form>
    </div>
  </section>
</BaseLayout>

<style>
  .hero-section {
    background: var(--color-bg-secondary);
    padding: var(--spacing-16) 0;
  }

  @media (min-width: 768px) {
    .hero-section {
      padding: var(--spacing-24) 0;
    }
  }

  .hero-content {
    text-align: center;
    margin-bottom: var(--spacing-16);
  }

  .hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-6);
    line-height: var(--line-height-tight);
  }

  @media (min-width: 768px) {
    .hero-title {
      font-size: var(--font-size-6xl);
    }
  }

  .hero-description {
    font-size: var(--font-size-xl);
    color: var(--color-text-secondary);
    max-width: 48rem;
    margin: 0 auto var(--spacing-8);
    line-height: var(--line-height-relaxed);
  }

  @media (min-width: 768px) {
    .hero-description {
      font-size: var(--font-size-2xl);
    }
  }

  .hero-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
    justify-content: center;
    align-items: center;
  }

  @media (min-width: 640px) {
    .hero-actions {
      flex-direction: row;
    }
  }

  .btn-icon {
    width: 1.25rem;
    height: 1.25rem;
    margin-left: var(--spacing-2);
  }

  .featured-section {
    margin-bottom: var(--spacing-16);
  }

  .section-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-8);
    text-align: center;
  }

  @media (min-width: 768px) {
    .section-title {
      font-size: var(--font-size-4xl);
    }
  }

  .recent-posts-section {
    padding: var(--spacing-16) 0;
    background-color: var(--color-bg-primary);
  }

  .section-header {
    text-align: center;
    margin-bottom: var(--spacing-12);
  }

  .section-description {
    font-size: var(--font-size-lg);
    color: var(--color-text-secondary);
    max-width: 32rem;
    margin: 0 auto;
  }

  .posts-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
    margin-bottom: var(--spacing-12);
  }

  @media (min-width: 768px) {
    .posts-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 1024px) {
    .posts-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  .empty-state {
    text-align: center;
    padding: var(--spacing-12) 0;
  }

  .empty-state p {
    color: var(--color-text-tertiary);
    font-size: var(--font-size-lg);
  }

  .view-all-container {
    text-align: center;
  }

  .view-all-link {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-base);
    font-weight: 500;
    color: var(--color-primary-600);
    background-color: var(--color-primary-50);
    border-radius: var(--border-radius-lg);
    text-decoration: none;
    transition: all var(--transition-normal);
  }

  .view-all-link:hover {
    background-color: var(--color-primary-100);
    color: var(--color-primary-700);
  }

  [data-theme="dark"] .view-all-link {
    color: var(--color-primary-400);
    background-color: rgba(59, 130, 246, 0.1);
  }

  [data-theme="dark"] .view-all-link:hover {
    background-color: rgba(59, 130, 246, 0.2);
    color: var(--color-primary-300);
  }

  .newsletter-section {
    padding: var(--spacing-16) 0;
    background-color: var(--color-bg-secondary);
  }

  .newsletter-container {
    max-width: 32rem;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
    text-align: center;
  }

  @media (min-width: 640px) {
    .newsletter-container {
      padding: 0 var(--spacing-6);
    }
  }

  @media (min-width: 1024px) {
    .newsletter-container {
      padding: 0 var(--spacing-8);
    }
  }

  .newsletter-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-4);
  }

  .newsletter-description {
    font-size: var(--font-size-lg);
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-8);
  }

  .newsletter-form {
    display: flex;
    gap: var(--spacing-4);
    max-width: 28rem;
    margin: 0 auto;
  }

  .newsletter-input {
    flex: 1;
  }
</style>
