---
import BaseLayout from '../layouts/BaseLayout.astro';
---

<BaseLayout 
  title="About Me" 
  description="Learn more about the author behind this blog and my journey in web development."
>
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 py-16 md:py-24">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <div class="mb-8">
          <img 
            src="/images/profile.jpg" 
            alt="Profile picture"
            class="w-32 h-32 rounded-full mx-auto object-cover shadow-lg"
            onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'"
          />
          <div class="w-32 h-32 rounded-full mx-auto bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-lg" style="display: none;">
            <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </div>
        </div>
        
        <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
          Hi, I'm <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">Your Name</span>
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-300 leading-relaxed">
          A passionate web developer sharing insights, tutorials, and experiences from the world of modern web development.
        </p>
      </div>
    </div>
  </section>

  <!-- About Content -->
  <section class="py-16 bg-white dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="prose prose-lg dark:prose-invert max-w-none">
        <h2>My Story</h2>
        <p>
          Welcome to my corner of the internet! I'm a web developer with a passion for creating 
          beautiful, functional, and accessible web experiences. My journey in tech started several 
          years ago, and I've been fascinated by the rapid evolution of web technologies ever since.
        </p>
        
        <p>
          Through this blog, I share my experiences, learnings, and insights about web development, 
          programming best practices, and the latest trends in technology. Whether you're a beginner 
          just starting your coding journey or an experienced developer looking to stay updated, 
          I hope you'll find something valuable here.
        </p>

        <h2>What I Do</h2>
        <p>
          I specialize in modern web development with a focus on:
        </p>
        <ul>
          <li><strong>Frontend Development:</strong> React, Vue.js, Astro, and modern JavaScript</li>
          <li><strong>Backend Development:</strong> Node.js, Python, and API design</li>
          <li><strong>Web Performance:</strong> Optimization techniques and best practices</li>
          <li><strong>Developer Experience:</strong> Tools and workflows that make development enjoyable</li>
        </ul>

        <h2>Why I Write</h2>
        <p>
          Writing helps me solidify my understanding of complex topics and gives me the opportunity 
          to give back to the developer community that has taught me so much. I believe in learning 
          in public and sharing knowledge to help others on their journey.
        </p>
        
        <p>
          Every article I write comes from real experience – whether it's solving a challenging 
          problem, learning a new technology, or discovering a better way to approach development.
        </p>
      </div>
    </div>
  </section>

  <!-- Skills & Technologies -->
  <section class="py-16 bg-gray-50 dark:bg-gray-800">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
        Technologies I Work With
      </h2>
      
      <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
        <div class="text-center">
          <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
            <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
            </svg>
          </div>
          <h3 class="font-medium text-gray-900 dark:text-white">JavaScript</h3>
        </div>
        
        <div class="text-center">
          <div class="w-16 h-16 bg-cyan-100 dark:bg-cyan-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
            <svg class="w-8 h-8 text-cyan-600 dark:text-cyan-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
            </svg>
          </div>
          <h3 class="font-medium text-gray-900 dark:text-white">React</h3>
        </div>
        
        <div class="text-center">
          <div class="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
            <svg class="w-8 h-8 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
            </svg>
          </div>
          <h3 class="font-medium text-gray-900 dark:text-white">Node.js</h3>
        </div>
        
        <div class="text-center">
          <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
            <svg class="w-8 h-8 text-purple-600 dark:text-purple-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
            </svg>
          </div>
          <h3 class="font-medium text-gray-900 dark:text-white">Astro</h3>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact -->
  <section class="py-16 bg-white dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
      <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-6">
        Let's Connect
      </h2>
      <p class="text-lg text-gray-600 dark:text-gray-300 mb-8">
        I'm always interested in connecting with fellow developers and discussing new ideas.
      </p>
      
      <div class="flex justify-center space-x-6">
        <a 
          href="https://twitter.com" 
          target="_blank" 
          rel="noopener noreferrer"
          class="text-gray-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
          aria-label="Twitter"
        >
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
          </svg>
        </a>
        <a 
          href="https://github.com" 
          target="_blank" 
          rel="noopener noreferrer"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          aria-label="GitHub"
        >
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
          </svg>
        </a>
        <a 
          href="https://linkedin.com" 
          target="_blank" 
          rel="noopener noreferrer"
          class="text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
          aria-label="LinkedIn"
        >
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
          </svg>
        </a>
        <a 
          href="mailto:<EMAIL>"
          class="text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
          aria-label="Email"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>
</BaseLayout>
