---
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import '../styles/global.css';

export interface Props {
  title: string;
  description?: string;
  image?: string;
  canonicalURL?: string;
}

const { title, description = 'A modern blog built with Astro', image, canonicalURL } = Astro.props;
const siteTitle = 'My Astro Blog';
const fullTitle = title ? `${title} | ${siteTitle}` : siteTitle;
---

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="generator" content={Astro.generator} />

    <!-- Primary Meta Tags -->
    <title>{fullTitle}</title>
    <meta name="title" content={fullTitle} />
    <meta name="description" content={description} />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content={canonicalURL || Astro.url} />
    <meta property="og:title" content={fullTitle} />
    <meta property="og:description" content={description} />
    {image && <meta property="og:image" content={image} />}

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={canonicalURL || Astro.url} />
    <meta property="twitter:title" content={fullTitle} />
    <meta property="twitter:description" content={description} />
    {image && <meta property="twitter:image" content={image} />}

    <!-- Canonical URL -->
    {canonicalURL && <link rel="canonical" href={canonicalURL} />}

    <!-- Preload fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
  </head>
  <body>
    <div class="page-layout">
      <Header />
      <main class="main-content">
        <slot />
      </main>
      <Footer />
    </div>

    <script>
      // Theme toggle functionality
      const theme = (() => {
        if (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) {
          return localStorage.getItem('theme');
        }
        if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
          return 'dark';
        }
        return 'light';
      })();

      if (theme === 'light') {
        document.documentElement.setAttribute('data-theme', 'light');
      } else {
        document.documentElement.setAttribute('data-theme', 'dark');
      }

      window.localStorage.setItem('theme', theme);
    </script>
  </body>
</html>

<style>
  .page-layout {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }

  .main-content {
    flex: 1;
  }
</style>