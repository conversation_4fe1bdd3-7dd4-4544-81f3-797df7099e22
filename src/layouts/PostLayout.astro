---
import BaseLayout from './BaseLayout.astro';
import CategoryTag from '../components/CategoryTag.astro';
import { formatDate } from '../utils/formatDate';

export interface Props {
  frontmatter: {
    title: string;
    description: string;
    pubDate: Date;
    updatedDate?: Date;
    heroImage?: string;
    category: string;
    tags: string[];
    author: string;
  };
}

const { frontmatter } = Astro.props;
const { title, description, pubDate, updatedDate, heroImage, category, tags, author } = frontmatter;
---

<BaseLayout title={title} description={description} image={heroImage}>
  <article class="post-article">
    <!-- Hero Image -->
    {heroImage && (
      <div class="hero-image-container">
        <img
          src={heroImage}
          alt={title}
          class="hero-image"
        />
      </div>
    )}

    <!-- Article Header -->
    <header class="article-header">
      <div class="category-container">
        <CategoryTag category={category} />
      </div>

      <h1 class="article-title">
        {title}
      </h1>

      <p class="article-description">
        {description}
      </p>

      <div class="article-meta">
        <div class="meta-item">
          <span>By</span>
          <span class="meta-value">{author}</span>
        </div>

        <div class="meta-item">
          <span>Published</span>
          <time datetime={pubDate.toISOString()} class="meta-value">
            {formatDate(pubDate)}
          </time>
        </div>

        {updatedDate && (
          <div class="meta-item">
            <span>Updated</span>
            <time datetime={updatedDate.toISOString()} class="meta-value">
              {formatDate(updatedDate)}
            </time>
          </div>
        )}
      </div>

      <!-- Tags -->
      {tags.length > 0 && (
        <div class="tags-container">
          <div class="tags-list">
            {tags.map((tag) => (
              <span class="tag">
                #{tag}
              </span>
            ))}
          </div>
        </div>
      )}
    </header>

    <!-- Article Content -->
    <div class="prose">
      <slot />
    </div>

    <!-- Article Footer -->
    <footer class="article-footer">
      <div class="footer-content">
        <div class="author-info">
          <p>Written by <span class="author-name">{author}</span></p>
        </div>

        <div class="footer-actions">
          <a href="/blog" class="back-link">
            ← Back to Blog
          </a>
        </div>
      </div>
    </footer>
  </article>
</BaseLayout>

<style>
  .post-article {
    max-width: 64rem;
    margin: 0 auto;
    padding: var(--spacing-4) var(--spacing-4) var(--spacing-8);
  }

  .hero-image-container {
    margin-bottom: var(--spacing-8);
  }

  .hero-image {
    width: 100%;
    height: 16rem;
    object-fit: cover;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
  }

  @media (min-width: 768px) {
    .hero-image {
      height: 24rem;
    }
  }

  .article-header {
    margin-bottom: var(--spacing-8);
  }

  .category-container {
    margin-bottom: var(--spacing-4);
  }

  .article-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-4);
    line-height: var(--line-height-tight);
  }

  @media (min-width: 768px) {
    .article-title {
      font-size: var(--font-size-5xl);
    }
  }

  .article-description {
    font-size: var(--font-size-xl);
    color: var(--color-text-secondary);
    margin-bottom: var(--spacing-6);
    line-height: var(--line-height-relaxed);
  }

  .article-meta {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--spacing-4);
    font-size: var(--font-size-sm);
    color: var(--color-text-tertiary);
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
  }

  .meta-value {
    font-weight: 500;
    color: var(--color-text-secondary);
  }

  .tags-container {
    margin-top: var(--spacing-6);
  }

  .tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-2);
  }

  .tag {
    padding: var(--spacing-1) var(--spacing-3);
    font-size: var(--font-size-xs);
    font-weight: 500;
    background-color: var(--color-bg-tertiary);
    color: var(--color-text-secondary);
    border-radius: var(--border-radius-full);
  }

  .article-footer {
    margin-top: var(--spacing-12);
    padding-top: var(--spacing-8);
    border-top: 1px solid var(--color-border);
  }

  .footer-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    gap: var(--spacing-4);
  }

  @media (min-width: 640px) {
    .footer-content {
      flex-direction: row;
      align-items: center;
    }
  }

  .author-info {
    font-size: var(--font-size-sm);
    color: var(--color-text-tertiary);
  }

  .author-name {
    font-weight: 500;
    color: var(--color-text-secondary);
  }

  .footer-actions {
    display: flex;
    gap: var(--spacing-4);
  }

  .back-link {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--color-primary-600);
    text-decoration: none;
    transition: color var(--transition-fast);
  }

  .back-link:hover {
    color: var(--color-primary-800);
  }

  [data-theme="dark"] .back-link {
    color: var(--color-primary-400);
  }

  [data-theme="dark"] .back-link:hover {
    color: var(--color-primary-300);
  }
</style>
