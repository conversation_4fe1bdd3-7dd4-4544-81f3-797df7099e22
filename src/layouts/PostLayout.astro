---
import BaseLayout from './BaseLayout.astro';
import CategoryTag from '../components/CategoryTag.astro';
import { formatDate } from '../utils/formatDate';

export interface Props {
  frontmatter: {
    title: string;
    description: string;
    pubDate: Date;
    updatedDate?: Date;
    heroImage?: string;
    category: string;
    tags: string[];
    author: string;
  };
}

const { frontmatter } = Astro.props;
const { title, description, pubDate, updatedDate, heroImage, category, tags, author } = frontmatter;
---

<BaseLayout title={title} description={description} image={heroImage}>
  <article class="max-w-4xl mx-auto px-4 py-8">
    <!-- Hero Image -->
    {heroImage && (
      <div class="mb-8">
        <img 
          src={heroImage} 
          alt={title}
          class="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg"
        />
      </div>
    )}
    
    <!-- Article Header -->
    <header class="mb-8">
      <div class="mb-4">
        <CategoryTag category={category} />
      </div>
      
      <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4 leading-tight">
        {title}
      </h1>
      
      <p class="text-xl text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
        {description}
      </p>
      
      <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
        <div class="flex items-center gap-2">
          <span>By</span>
          <span class="font-medium text-gray-700 dark:text-gray-300">{author}</span>
        </div>
        
        <div class="flex items-center gap-2">
          <span>Published</span>
          <time datetime={pubDate.toISOString()} class="font-medium text-gray-700 dark:text-gray-300">
            {formatDate(pubDate)}
          </time>
        </div>
        
        {updatedDate && (
          <div class="flex items-center gap-2">
            <span>Updated</span>
            <time datetime={updatedDate.toISOString()} class="font-medium text-gray-700 dark:text-gray-300">
              {formatDate(updatedDate)}
            </time>
          </div>
        )}
      </div>
      
      <!-- Tags -->
      {tags.length > 0 && (
        <div class="mt-6">
          <div class="flex flex-wrap gap-2">
            {tags.map((tag) => (
              <span class="px-3 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full">
                #{tag}
              </span>
            ))}
          </div>
        </div>
      )}
    </header>
    
    <!-- Article Content -->
    <div class="prose prose-lg dark:prose-invert max-w-none">
      <slot />
    </div>
    
    <!-- Article Footer -->
    <footer class="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
      <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          <p>Written by <span class="font-medium text-gray-700 dark:text-gray-300">{author}</span></p>
        </div>
        
        <div class="flex gap-4">
          <a 
            href="/blog" 
            class="text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
          >
            ← Back to Blog
          </a>
        </div>
      </div>
    </footer>
  </article>
</BaseLayout>
