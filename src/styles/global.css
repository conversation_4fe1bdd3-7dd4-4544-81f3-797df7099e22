@import "tailwindcss";

/* Custom styles for the blog */
@layer base {
    html {
        font-family: 'Inter', system-ui, sans-serif;
        scroll-behavior: smooth;
    }

    body {
        @apply antialiased;
    }
}

@layer components {

    /* Prose styles for blog content */
    .prose {
        @apply max-w-none;
    }

    .prose h1 {
        @apply text-3xl font-bold text-gray-900 dark:text-white mb-6 mt-8;
    }

    .prose h2 {
        @apply text-2xl font-bold text-gray-900 dark:text-white mb-4 mt-8;
    }

    .prose h3 {
        @apply text-xl font-semibold text-gray-900 dark:text-white mb-3 mt-6;
    }

    .prose p {
        @apply text-gray-700 dark:text-gray-300 mb-4 leading-relaxed;
    }

    .prose a {
        @apply text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline transition-colors;
    }

    .prose ul,
    .prose ol {
        @apply mb-4 pl-6;
    }

    .prose li {
        @apply text-gray-700 dark:text-gray-300 mb-2;
    }

    .prose blockquote {
        @apply border-l-4 border-blue-500 pl-4 italic text-gray-600 dark:text-gray-400 my-6;
    }

    .prose code {
        @apply bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 px-2 py-1 rounded text-sm font-mono;
    }

    .prose pre {
        @apply bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto mb-4;
    }

    .prose pre code {
        @apply bg-transparent text-gray-100 p-0;
    }

    .prose img {
        @apply rounded-lg shadow-lg my-6;
    }

    .prose table {
        @apply w-full border-collapse border border-gray-300 dark:border-gray-700 my-6;
    }

    .prose th,
    .prose td {
        @apply border border-gray-300 dark:border-gray-700 px-4 py-2 text-left;
    }

    .prose th {
        @apply bg-gray-50 dark:bg-gray-800 font-semibold;
    }
}

@layer utilities {

    /* Custom utility classes */
    .text-gradient {
        @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
    }

    .shadow-glow {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    }

    .transition-all-smooth {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
}