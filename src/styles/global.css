/* CSS Custom Properties for Design System */
:root {
    /* Colors */
    --color-white: #ffffff;
    --color-black: #000000;

    /* Gray Scale */
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;

    /* Primary Colors (Blue) */
    --color-primary-50: #eff6ff;
    --color-primary-100: #dbeafe;
    --color-primary-200: #bfdbfe;
    --color-primary-300: #93c5fd;
    --color-primary-400: #60a5fa;
    --color-primary-500: #3b82f6;
    --color-primary-600: #2563eb;
    --color-primary-700: #1d4ed8;
    --color-primary-800: #1e40af;
    --color-primary-900: #1e3a8a;

    /* Purple Colors */
    --color-purple-500: #8b5cf6;
    --color-purple-600: #7c3aed;

    /* Yellow Colors */
    --color-yellow-500: #eab308;

    /* Spacing Scale */
    --spacing-0: 0;
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    --spacing-24: 6rem;

    /* Font Sizes */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;

    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;

    /* Border Radius */
    --border-radius-sm: 0.125rem;
    --border-radius: 0.25rem;
    --border-radius-md: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;
    --border-radius-2xl: 1rem;
    --border-radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 200ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);

    /* Breakpoints */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
}

/* Dark Mode Variables */
[data-theme="dark"] {
    --color-bg-primary: var(--color-gray-900);
    --color-bg-secondary: var(--color-gray-800);
    --color-bg-tertiary: var(--color-gray-700);
    --color-text-primary: var(--color-white);
    --color-text-secondary: var(--color-gray-300);
    --color-text-tertiary: var(--color-gray-400);
    --color-border: var(--color-gray-700);
    --color-border-light: var(--color-gray-600);
}

/* Light Mode Variables */
[data-theme="light"],
:root {
    --color-bg-primary: var(--color-white);
    --color-bg-secondary: var(--color-gray-50);
    --color-bg-tertiary: var(--color-gray-100);
    --color-text-primary: var(--color-gray-900);
    --color-text-secondary: var(--color-gray-700);
    --color-text-tertiary: var(--color-gray-600);
    --color-border: var(--color-gray-200);
    --color-border-light: var(--color-gray-300);
}

/* Auto Dark Mode */
@media (prefers-color-scheme: dark) {
    :root:not([data-theme="light"]) {
        --color-bg-primary: var(--color-gray-900);
        --color-bg-secondary: var(--color-gray-800);
        --color-bg-tertiary: var(--color-gray-700);
        --color-text-primary: var(--color-white);
        --color-text-secondary: var(--color-gray-300);
        --color-text-tertiary: var(--color-gray-400);
        --color-border: var(--color-gray-700);
        --color-border-light: var(--color-gray-600);
    }
}

/* Reset and Base Styles */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    scroll-behavior: smooth;
    line-height: var(--line-height-normal);
}

body {
    background-color: var(--color-bg-primary);
    color: var(--color-text-primary);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 700;
    line-height: var(--line-height-tight);
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-4);
}

h1 {
    font-size: var(--font-size-4xl);
}

h2 {
    font-size: var(--font-size-3xl);
}

h3 {
    font-size: var(--font-size-2xl);
}

h4 {
    font-size: var(--font-size-xl);
}

h5 {
    font-size: var(--font-size-lg);
}

h6 {
    font-size: var(--font-size-base);
}

p {
    margin-bottom: var(--spacing-4);
    color: var(--color-text-secondary);
    line-height: var(--line-height-relaxed);
}

a {
    color: var(--color-primary-600);
    text-decoration: underline;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--color-primary-700);
}

[data-theme="dark"] a {
    color: var(--color-primary-400);
}

[data-theme="dark"] a:hover {
    color: var(--color-primary-300);
}

ul,
ol {
    margin-bottom: var(--spacing-4);
    padding-left: var(--spacing-6);
}

li {
    margin-bottom: var(--spacing-2);
    color: var(--color-text-secondary);
}

img {
    max-width: 100%;
    height: auto;
}

button {
    font-family: inherit;
    cursor: pointer;
}

input,
textarea,
select {
    font-family: inherit;
}

/* Focus Styles */
:focus-visible {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
}

/* Layout Utilities */
.container {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

@media (min-width: 640px) {
    .container {
        padding: 0 var(--spacing-6);
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 var(--spacing-8);
    }
}

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.flex-wrap {
    flex-wrap: wrap;
}

.items-center {
    align-items: center;
}

.items-start {
    align-items: flex-start;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.justify-start {
    justify-content: flex-start;
}

.grid {
    display: grid;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
}

.gap-2 {
    gap: var(--spacing-2);
}

.gap-4 {
    gap: var(--spacing-4);
}

.gap-6 {
    gap: var(--spacing-6);
}

.gap-8 {
    gap: var(--spacing-8);
}

/* Responsive Grid */
@media (min-width: 768px) {
    .md\\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .md\\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .md\\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
}

@media (min-width: 1024px) {
    .lg\\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .lg\\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
}

/* Spacing Utilities */
.m-0 {
    margin: 0;
}

.m-auto {
    margin: auto;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.mb-2 {
    margin-bottom: var(--spacing-2);
}

.mb-3 {
    margin-bottom: var(--spacing-3);
}

.mb-4 {
    margin-bottom: var(--spacing-4);
}

.mb-6 {
    margin-bottom: var(--spacing-6);
}

.mb-8 {
    margin-bottom: var(--spacing-8);
}

.mb-12 {
    margin-bottom: var(--spacing-12);
}

.mb-16 {
    margin-bottom: var(--spacing-16);
}

.mt-4 {
    margin-top: var(--spacing-4);
}

.mt-6 {
    margin-top: var(--spacing-6);
}

.mt-8 {
    margin-top: var(--spacing-8);
}

.p-2 {
    padding: var(--spacing-2);
}

.p-3 {
    padding: var(--spacing-3);
}

.p-4 {
    padding: var(--spacing-4);
}

.p-6 {
    padding: var(--spacing-6);
}

.p-8 {
    padding: var(--spacing-8);
}

.px-2 {
    padding-left: var(--spacing-2);
    padding-right: var(--spacing-2);
}

.px-3 {
    padding-left: var(--spacing-3);
    padding-right: var(--spacing-3);
}

.px-4 {
    padding-left: var(--spacing-4);
    padding-right: var(--spacing-4);
}

.px-6 {
    padding-left: var(--spacing-6);
    padding-right: var(--spacing-6);
}

.py-1 {
    padding-top: var(--spacing-1);
    padding-bottom: var(--spacing-1);
}

.py-2 {
    padding-top: var(--spacing-2);
    padding-bottom: var(--spacing-2);
}

.py-3 {
    padding-top: var(--spacing-3);
    padding-bottom: var(--spacing-3);
}

.py-8 {
    padding-top: var(--spacing-8);
    padding-bottom: var(--spacing-8);
}

.py-12 {
    padding-top: var(--spacing-12);
    padding-bottom: var(--spacing-12);
}

.py-16 {
    padding-top: var(--spacing-16);
    padding-bottom: var(--spacing-16);
}

.py-24 {
    padding-top: var(--spacing-24);
    padding-bottom: var(--spacing-24);
}

/* Text Utilities */
.text-xs {
    font-size: var(--font-size-xs);
}

.text-sm {
    font-size: var(--font-size-sm);
}

.text-base {
    font-size: var(--font-size-base);
}

.text-lg {
    font-size: var(--font-size-lg);
}

.text-xl {
    font-size: var(--font-size-xl);
}

.text-2xl {
    font-size: var(--font-size-2xl);
}

.text-3xl {
    font-size: var(--font-size-3xl);
}

.text-4xl {
    font-size: var(--font-size-4xl);
}

.text-5xl {
    font-size: var(--font-size-5xl);
}

.text-6xl {
    font-size: var(--font-size-6xl);
}

.font-medium {
    font-weight: 500;
}

.font-semibold {
    font-weight: 600;
}

.font-bold {
    font-weight: 700;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.leading-tight {
    line-height: var(--line-height-tight);
}

.leading-normal {
    line-height: var(--line-height-normal);
}

.leading-relaxed {
    line-height: var(--line-height-relaxed);
}

.text-transparent {
    color: transparent;
}

.text-white {
    color: var(--color-white);
}

.text-gray-300 {
    color: var(--color-gray-300);
}

.text-gray-400 {
    color: var(--color-gray-400);
}

.text-gray-500 {
    color: var(--color-gray-500);
}

.text-gray-600 {
    color: var(--color-gray-600);
}

.text-gray-700 {
    color: var(--color-gray-700);
}

.text-gray-900 {
    color: var(--color-gray-900);
}

.text-blue-600 {
    color: var(--color-primary-600);
}

.text-blue-400 {
    color: var(--color-primary-400);
}

/* Background Colors */
.bg-white {
    background-color: var(--color-white);
}

.bg-gray-50 {
    background-color: var(--color-gray-50);
}

.bg-gray-100 {
    background-color: var(--color-gray-100);
}

.bg-gray-800 {
    background-color: var(--color-gray-800);
}

.bg-gray-900 {
    background-color: var(--color-gray-900);
}

.bg-blue-600 {
    background-color: var(--color-primary-600);
}

.bg-blue-700 {
    background-color: var(--color-primary-700);
}

/* Border Utilities */
.border {
    border: 1px solid var(--color-border);
}

.border-t {
    border-top: 1px solid var(--color-border);
}

.border-b {
    border-bottom: 1px solid var(--color-border);
}

.border-gray-200 {
    border-color: var(--color-gray-200);
}

.border-gray-700 {
    border-color: var(--color-gray-700);
}

.rounded {
    border-radius: var(--border-radius);
}

.rounded-md {
    border-radius: var(--border-radius-md);
}

.rounded-lg {
    border-radius: var(--border-radius-lg);
}

.rounded-xl {
    border-radius: var(--border-radius-xl);
}

.rounded-2xl {
    border-radius: var(--border-radius-2xl);
}

.rounded-full {
    border-radius: var(--border-radius-full);
}

/* Shadow Utilities */
.shadow-sm {
    box-shadow: var(--shadow-sm);
}

.shadow {
    box-shadow: var(--shadow);
}

.shadow-md {
    box-shadow: var(--shadow-md);
}

.shadow-lg {
    box-shadow: var(--shadow-lg);
}

.shadow-xl {
    box-shadow: var(--shadow-xl);
}

/* Width and Height */
.w-full {
    width: 100%;
}

.w-16 {
    width: var(--spacing-16);
}

.w-32 {
    width: 8rem;
}

.h-full {
    height: 100%;
}

.h-16 {
    width: var(--spacing-16);
}

.h-32 {
    height: 8rem;
}

.h-64 {
    height: 16rem;
}

.min-h-screen {
    min-height: 100vh;
}

/* Position */
.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.sticky {
    position: sticky;
}

.top-0 {
    top: 0;
}

.top-4 {
    top: var(--spacing-4);
}

.right-4 {
    right: var(--spacing-4);
}

.left-4 {
    left: var(--spacing-4);
}

.z-50 {
    z-index: 50;
}

/* Display */
.block {
    display: block;
}

.inline-block {
    display: inline-block;
}

.inline-flex {
    display: inline-flex;
}

.hidden {
    display: none;
}

/* Overflow */
.overflow-hidden {
    overflow: hidden;
}

.overflow-x-auto {
    overflow-x: auto;
}

/* Transitions */
.transition-colors {
    transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast);
}

.transition-transform {
    transition: transform var(--transition-normal);
}

.transition-shadow {
    transition: box-shadow var(--transition-normal);
}

.transition-all {
    transition: all var(--transition-normal);
}

/* Transform */
.scale-105 {
    transform: scale(1.05);
}

.translate-x-1 {
    transform: translateX(0.25rem);
}

.-translate-y-4 {
    transform: translateY(-1rem);
}

/* Hover Effects */
.hover\\:bg-gray-50:hover {
    background-color: var(--color-gray-50);
}

.hover\\:bg-gray-100:hover {
    background-color: var(--color-gray-100);
}

.hover\\:bg-blue-700:hover {
    background-color: var(--color-primary-700);
}

.hover\\:text-blue-600:hover {
    color: var(--color-primary-600);
}

.hover\\:text-blue-800:hover {
    color: var(--color-primary-800);
}

.hover\\:shadow-lg:hover {
    box-shadow: var(--shadow-lg);
}

.hover\\:shadow-xl:hover {
    box-shadow: var(--shadow-xl);
}

.hover\\:scale-105:hover {
    transform: scale(1.05);
}

/* Group Hover */
.group:hover .group-hover\\:scale-105 {
    transform: scale(1.05);
}

.group:hover .group-hover\\:text-blue-600 {
    color: var(--color-primary-600);
}

.group:hover .group-hover\\:text-blue-400 {
    color: var(--color-primary-400);
}

/* Component Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-base);
    font-weight: 500;
    border-radius: var(--border-radius-lg);
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
}

.btn:focus-visible {
    outline: 2px solid var(--color-primary-500);
    outline-offset: 2px;
}

.btn-primary {
    background-color: var(--color-primary-600);
    color: var(--color-white);
}

.btn-primary:hover {
    background-color: var(--color-primary-700);
    color: var(--color-white);
}

.btn-secondary {
    background-color: var(--color-bg-tertiary);
    color: var(--color-text-primary);
    border: 1px solid var(--color-border);
}

.btn-secondary:hover {
    background-color: var(--color-bg-secondary);
    color: var(--color-text-primary);
}

/* Card Component */
.card {
    background-color: var(--color-bg-primary);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    transition: box-shadow var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-md);
}

/* Prose Styles for Blog Content */
.prose {
    max-width: none;
    color: var(--color-text-secondary);
    line-height: var(--line-height-relaxed);
}

.prose h1 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-6);
    margin-top: var(--spacing-8);
}

.prose h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-4);
    margin-top: var(--spacing-8);
}

.prose h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-3);
    margin-top: var(--spacing-6);
}

.prose p {
    margin-bottom: var(--spacing-4);
    color: var(--color-text-secondary);
    line-height: var(--line-height-relaxed);
}

.prose a {
    color: var(--color-primary-600);
    text-decoration: underline;
    transition: color var(--transition-fast);
}

.prose a:hover {
    color: var(--color-primary-800);
}

[data-theme="dark"] .prose a {
    color: var(--color-primary-400);
}

[data-theme="dark"] .prose a:hover {
    color: var(--color-primary-300);
}

.prose ul,
.prose ol {
    margin-bottom: var(--spacing-4);
    padding-left: var(--spacing-6);
}

.prose li {
    margin-bottom: var(--spacing-2);
    color: var(--color-text-secondary);
}

.prose blockquote {
    border-left: 4px solid var(--color-primary-500);
    padding-left: var(--spacing-4);
    font-style: italic;
    color: var(--color-text-tertiary);
    margin: var(--spacing-6) 0;
}

.prose code {
    background-color: var(--color-bg-tertiary);
    color: var(--color-text-primary);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
}

.prose pre {
    background-color: var(--color-gray-900);
    color: var(--color-gray-100);
    padding: var(--spacing-4);
    border-radius: var(--border-radius-lg);
    overflow-x: auto;
    margin-bottom: var(--spacing-4);
}

.prose pre code {
    background-color: transparent;
    color: var(--color-gray-100);
    padding: 0;
}

.prose img {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    margin: var(--spacing-6) 0;
}

.prose table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid var(--color-border);
    margin: var(--spacing-6) 0;
}

.prose th,
.prose td {
    border: 1px solid var(--color-border);
    padding: var(--spacing-4) var(--spacing-4);
    text-align: left;
}

.prose th {
    background-color: var(--color-bg-secondary);
    font-weight: 600;
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(to right, var(--color-primary-600), var(--color-purple-600));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.aspect-video {
    aspect-ratio: 16 / 9;
}

.aspect-square {
    aspect-ratio: 1 / 1;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.backdrop-blur {
    backdrop-filter: blur(8px);
}

.space-x-2>*+* {
    margin-left: var(--spacing-2);
}

.space-x-4>*+* {
    margin-left: var(--spacing-4);
}

.space-x-6>*+* {
    margin-left: var(--spacing-6);
}

.space-y-2>*+* {
    margin-top: var(--spacing-2);
}

.space-y-4>*+* {
    margin-top: var(--spacing-4);
}

/* Responsive Utilities */
@media (min-width: 640px) {
    .sm\\:block {
        display: block;
    }

    .sm\\:hidden {
        display: none;
    }

    .sm\\:flex {
        display: flex;
    }

    .sm\\:flex-row {
        flex-direction: row;
    }

    .sm\\:px-6 {
        padding-left: var(--spacing-6);
        padding-right: var(--spacing-6);
    }

    .sm\\:text-center {
        text-align: center;
    }
}

@media (min-width: 768px) {
    .md\\:block {
        display: block;
    }

    .md\\:hidden {
        display: none;
    }

    .md\\:flex {
        display: flex;
    }

    .md\\:flex-row {
        flex-direction: row;
    }

    .md\\:text-2xl {
        font-size: var(--font-size-2xl);
    }

    .md\\:text-3xl {
        font-size: var(--font-size-3xl);
    }

    .md\\:text-4xl {
        font-size: var(--font-size-4xl);
    }

    .md\\:text-5xl {
        font-size: var(--font-size-5xl);
    }

    .md\\:text-6xl {
        font-size: var(--font-size-6xl);
    }

    .md\\:py-24 {
        padding-top: var(--spacing-24);
        padding-bottom: var(--spacing-24);
    }

    .md\\:col-span-2 {
        grid-column: span 2 / span 2;
    }
}

@media (min-width: 1024px) {
    .lg\\:block {
        display: block;
    }

    .lg\\:hidden {
        display: none;
    }

    .lg\\:flex {
        display: flex;
    }

    .lg\\:px-8 {
        padding-left: var(--spacing-8);
        padding-right: var(--spacing-8);
    }
}

/* Dark Mode Responsive Text Colors */
[data-theme="dark"] .text-white {
    color: var(--color-white);
}

[data-theme="dark"] .text-gray-100 {
    color: var(--color-gray-100);
}

[data-theme="dark"] .text-gray-300 {
    color: var(--color-gray-300);
}

[data-theme="dark"] .text-gray-400 {
    color: var(--color-gray-400);
}

/* Dark Mode Responsive Background Colors */
[data-theme="dark"] .bg-gray-800 {
    background-color: var(--color-gray-800);
}

[data-theme="dark"] .bg-gray-900 {
    background-color: var(--color-gray-900);
}

/* Dark Mode Responsive Border Colors */
[data-theme="dark"] .border-gray-700 {
    border-color: var(--color-gray-700);
}

/* Form Styles */
input[type="text"],
input[type="email"],
input[type="search"],
textarea,
select {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-4);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius-lg);
    background-color: var(--color-bg-primary);
    color: var(--color-text-primary);
    font-size: var(--font-size-base);
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="search"]:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Navigation Styles */
.nav-link {
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--border-radius-md);
    color: var(--color-text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: all var(--transition-fast);
}

.nav-link:hover {
    color: var(--color-primary-600);
    background-color: var(--color-bg-tertiary);
}

.nav-link.active {
    color: var(--color-primary-600);
    background-color: var(--color-primary-50);
}

[data-theme="dark"] .nav-link.active {
    color: var(--color-primary-400);
    background-color: rgba(59, 130, 246, 0.1);
}

/* Special Effects */
.shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.gradient-bg {
    background: linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-white) 50%, var(--color-purple-50) 100%);
}

[data-theme="dark"] .gradient-bg {
    background: linear-gradient(135deg, var(--color-gray-900) 0%, var(--color-gray-900) 50%, var(--color-gray-800) 100%);
}