import { defineConfig } from 'astro/config';
import tailwind from '@astrojs/tailwind';
import mdx from '@astrojs/mdx';
import sitemap from '@astrojs/sitemap';
import partytown from '@astrojs/partytown';

export default defineConfig({
  site: 'https://your-blog.com',
  integrations: [
    tailwind(),
    mdx(),
    sitemap(),
    partytown({
      config: {
        forward: ['dataLayer.push'],
      },
    }),
  ],
  markdown: {
    shikiConfig: {
      theme: 'github-dark-dimmed',
      langs: ['js', 'ts', 'html', 'css', 'astro', 'bash'],
      wrap: true,
    },
  },
  vite: {
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            'astro': ['astro'],
          },
        },
      },
    },
    optimizeDeps: {
      include: ['reading-time', 'github-slugger'],
    },
    plugins: [
      
    ],
  },
  compressHTML: true,  
  build: {
    inlineStylesheets: 'auto',  
  },
});