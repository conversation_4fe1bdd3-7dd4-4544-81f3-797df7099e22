import { defineConfig } from 'astro/config';
import mdx from '@astrojs/mdx';
import sitemap from '@astrojs/sitemap';
import partytown from '@astrojs/partytown';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
  site: 'https://your-blog.com',
  integrations: [
    mdx(),
    sitemap(),
    partytown({
      config: {
        forward: ['dataLayer.push'],
      },
    }),
  ],
  markdown: {
    shikiConfig: {
      theme: 'github-dark-dimmed',
      langs: ['js', 'ts', 'html', 'css', 'astro', 'bash'],
      wrap: true,
    },
  },
  vite: {
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            'astro': ['astro'],
          },
        },
      },
    },
    optimizeDeps: {
      include: ['reading-time', 'github-slugger'],
    },
    plugins: [
      tailwindcss(),
    ],
  },
  compressHTML: true,
  build: {
    inlineStylesheets: 'auto',
  },
});